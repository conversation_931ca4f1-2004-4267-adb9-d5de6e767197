@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 环境检查脚本 (Windows版本)
:: 用于检查系统是否满足部署要求

echo ========================================
echo         环境检查报告
echo ========================================
echo.

:: 检查操作系统
echo --- 操作系统检查 ---
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo ✓ 操作系统: Windows %VERSION%

:: 检查系统架构
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✓ 系统架构: 64位 ^(兼容^)
) else (
    echo ⚠ 系统架构: %PROCESSOR_ARCHITECTURE% ^(可能不兼容^)
)
echo.

:: 检查硬件资源
echo --- 硬件资源检查 ---

:: 检查CPU核心数
for /f "tokens=2 delims==" %%i in ('wmic cpu get NumberOfCores /value ^| find "="') do set CPU_CORES=%%i
if !CPU_CORES! geq 4 (
    echo ✓ CPU核心数: !CPU_CORES! ^(推荐4核以上^)
) else (
    echo ⚠ CPU核心数: !CPU_CORES! ^(建议4核以上以获得更好性能^)
)

:: 检查内存
for /f "tokens=2 delims==" %%i in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do set TOTAL_MEM=%%i
set /a TOTAL_MEM_GB=!TOTAL_MEM!/1024/1024/1024
if !TOTAL_MEM_GB! geq 8 (
    echo ✓ 系统内存: !TOTAL_MEM_GB!GB ^(推荐8GB以上^)
) else if !TOTAL_MEM_GB! geq 4 (
    echo ⚠ 系统内存: !TOTAL_MEM_GB!GB ^(建议8GB以上，当前可用但可能影响性能^)
) else (
    echo ✗ 系统内存: !TOTAL_MEM_GB!GB ^(不足4GB，可能无法正常运行^)
)

:: 检查磁盘空间
for /f "tokens=3" %%i in ('dir /-c ^| find "可用字节"') do set AVAILABLE_SPACE=%%i
set AVAILABLE_SPACE=!AVAILABLE_SPACE:,=!
set /a AVAILABLE_SPACE_GB=!AVAILABLE_SPACE!/1024/1024/1024
if !AVAILABLE_SPACE_GB! geq 50 (
    echo ✓ 可用磁盘空间: !AVAILABLE_SPACE_GB!GB ^(推荐50GB以上^)
) else if !AVAILABLE_SPACE_GB! geq 20 (
    echo ⚠ 可用磁盘空间: !AVAILABLE_SPACE_GB!GB ^(建议50GB以上，当前可用但可能不足^)
) else (
    echo ✗ 可用磁盘空间: !AVAILABLE_SPACE_GB!GB ^(不足20GB，可能无法正常部署^)
)
echo.

:: 检查网络连接
echo --- 网络连接检查 ---
ping -n 1 8.8.8.8 >nul 2>&1
if errorlevel 1 (
    echo ✗ 无法连接到互联网，请检查网络设置
) else (
    echo ✓ 互联网连接正常
)

:: 检查DNS解析
nslookup google.com >nul 2>&1
if errorlevel 1 (
    echo ⚠ DNS解析可能有问题
) else (
    echo ✓ DNS解析正常
)

:: 检查Docker Hub连接
powershell -Command "try { Invoke-WebRequest -Uri 'https://hub.docker.com' -TimeoutSec 5 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ⚠ 无法访问Docker Hub，可能需要配置镜像源
) else (
    echo ✓ 可以访问Docker Hub
)
echo.

:: 检查软件环境
echo --- 软件环境检查 ---

:: 检查Docker Desktop
docker --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Docker未安装
    echo   请从 https://www.docker.com/products/docker-desktop 下载安装
) else (
    for /f "tokens=3" %%i in ('docker --version') do set DOCKER_VERSION=%%i
    echo ✓ Docker已安装: !DOCKER_VERSION!
    
    :: 检查Docker服务状态
    docker ps >nul 2>&1
    if errorlevel 1 (
        echo ⚠ Docker服务未运行，请启动Docker Desktop
    ) else (
        echo ✓ Docker服务运行正常
    )
)

:: 检查Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Docker Compose不可用
) else (
    for /f "tokens=3" %%i in ('docker-compose --version') do set COMPOSE_VERSION=%%i
    echo ✓ Docker Compose可用: !COMPOSE_VERSION!
)

:: 检查Git
git --version >nul 2>&1
if errorlevel 1 (
    echo ⚠ Git未安装，建议安装以便获取项目代码
) else (
    for /f "tokens=3" %%i in ('git --version') do set GIT_VERSION=%%i
    echo ✓ Git已安装: !GIT_VERSION!
)

:: 检查PowerShell
powershell -Command "exit 0" >nul 2>&1
if errorlevel 1 (
    echo ⚠ PowerShell不可用
) else (
    echo ✓ PowerShell可用
)
echo.

:: 检查端口占用
echo --- 端口占用检查 ---
set PORTS=3000 8000 3306 7474 7687 7272 7273 5432 9000 9001 8888 8474
set PORT_NAMES=前端界面 后端API MySQL Neo4j-HTTP Neo4j-Bolt R2R-API R2R-Dashboard PostgreSQL MinIO-API MinIO-Console Memory-API Neo4j-Memory

set i=0
for %%p in (%PORTS%) do (
    set /a i+=1
    netstat -an | find ":%%p " >nul 2>&1
    if errorlevel 1 (
        echo ✓ 端口 %%p 可用
    ) else (
        echo ⚠ 端口 %%p 已被占用
    )
)
echo.

:: 检查权限
echo --- 权限检查 ---
:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠ 当前不是管理员权限，可能需要管理员权限来安装软件
) else (
    echo ✓ 具有管理员权限
)

:: 检查当前目录写权限
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo ✓ 当前目录可写
    del test_write.tmp >nul 2>&1
) else (
    echo ✗ 当前目录不可写，无法创建配置文件
)
echo.

:: 生成建议
echo --- 建议和下一步 ---
echo.
echo 基于检查结果，建议：
echo.

:: 检查是否需要安装Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo 1. 安装Docker Desktop:
    echo    - 访问: https://www.docker.com/products/docker-desktop
    echo    - 下载Windows版本并安装
    echo    - 安装后重启计算机
    echo.
)

:: 内存建议
if !TOTAL_MEM_GB! lss 8 (
    echo 2. 内存优化建议:
    echo    - 关闭不必要的程序以释放内存
    echo    - 考虑只部署单个应用而非全部应用
    echo    - 如果可能，增加系统内存到8GB以上
    echo.
)

:: 磁盘空间建议
if !AVAILABLE_SPACE_GB! lss 20 (
    echo 3. 磁盘空间优化:
    echo    - 清理不必要的文件
    echo    - 使用磁盘清理工具
    echo    - 考虑扩展磁盘空间
    echo.
)

echo 4. 准备API密钥:
echo    - OpenAI API密钥 ^(必需^)
echo    - DeepSeek API密钥 ^(可选^)
echo.

echo 5. 运行部署脚本:
echo    双击运行: quick-deploy.bat
echo    或在命令行中运行: quick-deploy.bat
echo.

echo ========================================
echo         检查完成
echo ========================================
echo.
echo 按任意键退出...
pause >nul
