volumes:
  postgres_data:
    name: postgres_data
  minio_data:
    name: minio_data

services:
  postgres:
    image: pgvector/pgvector:pg16
    env_file:
      - ./env/postgres.env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: on-failure
    command: >
      postgres
      -c max_connections=1024

  minio:
    image: minio/minio
    env_file:
      - ./env/minio.env
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: on-failure
    command: server /data --console-address ":9001"

  graph_clustering:
    image: ragtoriches/cluster-prod
    ports:
      - "7276:7276"
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:7276/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  r2r:
    image: sciphiai/r2r:latest
    ports:
      - "7272:7272"
    env_file:
      - ./env/r2r.env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7272/v3/health"]
      interval: 6s
      timeout: 5s
      retries: 5
    restart: on-failure
    volumes:
      - ./user_configs:/app/user_configs
      - ./user_tools:/app/user_tools
    extra_hosts:
      - host.docker.internal:host-gateway

  r2r-dashboard:
    image: sciphiai/r2r-dashboard:1.0.3
    env_file:
      - ./env/r2r-dashboard.env
    ports:
      - "7273:3000"
