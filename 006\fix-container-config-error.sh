#!/bin/bash

# 修复Docker Compose ContainerConfig错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

echo "========================================"
echo "   修复ContainerConfig错误"
echo "========================================"
echo

# 检查是否在正确的目录
if [[ ! -f "docker-compose.yml" ]]; then
    print_error "请在chatdb目录下运行此脚本"
    exit 1
fi

print_info "开始修复ContainerConfig错误..."

# 1. 完全停止所有服务
print_info "1. 停止所有Docker Compose服务..."
docker-compose down --remove-orphans 2>/dev/null || true

# 2. 删除所有相关容器
print_info "2. 删除相关容器..."
docker-compose rm -f 2>/dev/null || true

# 删除可能存在的容器
docker rm -f chatdb-frontend chatdb-backend chatdb-mysql chatdb-neo4j 2>/dev/null || true

# 3. 删除相关镜像
print_info "3. 删除相关镜像..."
docker rmi chatdb_frontend 2>/dev/null || true
docker rmi chatdb_backend 2>/dev/null || true
docker rmi chatdb-frontend 2>/dev/null || true
docker rmi chatdb-backend 2>/dev/null || true

# 4. 清理Docker系统
print_info "4. 清理Docker系统..."
docker system prune -f

# 5. 清理Docker构建缓存
print_info "5. 清理构建缓存..."
docker builder prune -f

# 6. 检查Dockerfile
print_info "6. 检查Dockerfile..."
if [[ -f "frontend/Dockerfile" ]]; then
    print_success "前端Dockerfile存在"
else
    print_error "前端Dockerfile不存在"
fi

if [[ -f "backend/Dockerfile" ]]; then
    print_success "后端Dockerfile存在"
else
    print_error "后端Dockerfile不存在"
fi

# 7. 重新构建镜像
print_info "7. 重新构建所有镜像..."

# 先构建后端
print_info "构建后端镜像..."
if docker-compose build --no-cache backend; then
    print_success "后端镜像构建成功"
else
    print_error "后端镜像构建失败"
    exit 1
fi

# 再构建前端
print_info "构建前端镜像..."
if docker-compose build --no-cache frontend; then
    print_success "前端镜像构建成功"
else
    print_error "前端镜像构建失败"
    
    # 尝试修复前端构建问题
    print_info "尝试修复前端构建问题..."
    
    # 检查package.json
    if [[ -f "frontend/package.json" ]]; then
        print_info "检查package.json..."
        if grep -q "chart.js" frontend/package.json; then
            print_success "chart.js依赖存在"
        else
            print_warning "chart.js依赖缺失，正在添加..."
            # 备份并修复package.json
            cp frontend/package.json frontend/package.json.backup
            sed -i 's/"axios": "\^1.3.4",/"axios": "^1.3.4",\n    "chart.js": "^4.4.0",/' frontend/package.json
        fi
    fi
    
    # 再次尝试构建前端
    print_info "再次尝试构建前端..."
    if docker-compose build --no-cache frontend; then
        print_success "前端镜像构建成功"
    else
        print_error "前端镜像构建仍然失败"
        print_info "查看构建日志："
        docker-compose build frontend
        exit 1
    fi
fi

# 8. 启动服务
print_info "8. 启动所有服务..."
if docker-compose up -d; then
    print_success "服务启动成功"
else
    print_error "服务启动失败"
    print_info "尝试逐个启动服务..."
    
    # 先启动数据库服务
    print_info "启动数据库服务..."
    docker-compose up -d mysql neo4j
    sleep 15
    
    # 再启动后端
    print_info "启动后端服务..."
    docker-compose up -d backend
    sleep 10
    
    # 最后启动前端
    print_info "启动前端服务..."
    docker-compose up -d frontend
fi

# 9. 等待服务启动
print_info "9. 等待服务完全启动..."
sleep 30

# 10. 检查服务状态
print_info "10. 检查服务状态..."
docker-compose ps

# 11. 测试服务
print_info "11. 测试服务连接..."

# 测试后端
max_attempts=5
attempt=1
while [ $attempt -le $max_attempts ]; do
    if curl -s http://localhost:8000/api/ > /dev/null 2>&1; then
        print_success "后端API连接成功"
        break
    else
        print_warning "尝试 $attempt/$max_attempts: 等待后端启动..."
        sleep 10
        ((attempt++))
    fi
done

# 测试前端
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    print_success "前端连接成功"
else
    print_warning "前端可能还在启动中..."
fi

# 12. 初始化数据库
print_info "12. 初始化数据库..."
sleep 5
if docker-compose exec -T backend python init_db.py 2>/dev/null; then
    print_success "数据库初始化成功"
else
    print_warning "数据库初始化可能失败，请稍后手动执行"
fi

echo
print_success "========== 修复完成 =========="
print_info "服务状态："
docker-compose ps

echo
print_info "访问地址："
print_info "  前端: http://localhost:3000"
print_info "  后端API: http://localhost:8000/docs"
print_info "  Neo4j: http://localhost:7474"

echo
print_info "如果服务仍有问题，请查看日志："
print_info "  docker-compose logs frontend"
print_info "  docker-compose logs backend"
