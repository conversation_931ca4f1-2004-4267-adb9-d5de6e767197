{"compilerOptions": {"target": "es2017", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "declaration": true, "moduleResolution": "node", "strict": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "lib": ["es2017", "dom"], "sourceMap": true, "types": ["node", "jest", "@types/jest"], "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/__tests__/*", "**/*.spec.ts"]}