<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简洁编辑器界面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* AI工具栏 */
        .ai-toolbar {
            background: #fff;
            border-bottom: 1px solid #e8e8e8;
            padding: 12px 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .ai-tools-container {
            display: flex;
            gap: 32px;
            align-items: center;
        }
        
        .ai-tool-btn {
            height: 40px;
            padding: 0 16px;
            font-size: 14px;
            color: #666;
            border: none;
            background: transparent;
            display: flex;
            align-items: center;
            gap: 6px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .ai-tool-btn:hover {
            background: #f0f0f0;
            color: #1890ff;
        }
        
        .ai-tool-btn:focus {
            background: #f0f0f0;
            color: #1890ff;
            outline: none;
        }
        
        .ai-tool-icon {
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
        }
        
        .content-placeholder {
            text-align: center;
            color: #999;
            font-size: 16px;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <!-- AI工具栏 -->
    <div class="ai-toolbar">
        <div class="ai-tools-container">
            <button class="ai-tool-btn" onclick="handleToolClick('AI写作')">
                <span class="ai-tool-icon">🤖</span>
                AI写作
            </button>
            
            <button class="ai-tool-btn" onclick="handleToolClick('AI对效')">
                <span class="ai-tool-icon">📄</span>
                AI对效
            </button>
            
            <button class="ai-tool-btn" onclick="handleToolClick('AI润色')">
                <span class="ai-tool-icon">✨</span>
                AI润色
            </button>
            
            <button class="ai-tool-btn" onclick="handleToolClick('deepseek')">
                <span class="ai-tool-icon">🔍</span>
                deepseek
            </button>
            
            <button class="ai-tool-btn" onclick="handleToolClick('AI对比')">
                <span class="ai-tool-icon">⚖️</span>
                AI对比
            </button>
            
            <button class="ai-tool-btn" onclick="handleToolClick('排版')">
                <span class="ai-tool-icon">📚</span>
                排版
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <div class="content-placeholder">
            请在此输入内容
        </div>
    </div>

    <script>
        function handleToolClick(toolName) {
            if (toolName === 'AI写作') {
                alert('跳转到AI写作向导');
            } else {
                alert(`点击了 ${toolName} 工具`);
            }
        }

        // 添加键盘事件支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                const focused = document.activeElement;
                if (focused && focused.classList.contains('ai-tool-btn')) {
                    focused.click();
                }
            }
        });
    </script>
</body>
</html>
