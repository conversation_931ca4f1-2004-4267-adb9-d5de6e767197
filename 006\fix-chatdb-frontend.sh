#!/bin/bash

# ChatDB前端依赖修复脚本
# 解决chart.js缺失问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

echo "========================================"
echo "   ChatDB前端依赖修复脚本"
echo "========================================"
echo

# 检查是否在正确的目录
if [[ ! -f "docker-compose.yml" ]]; then
    print_error "请在chatdb目录下运行此脚本"
    exit 1
fi

print_info "开始修复ChatDB前端依赖问题..."

# 停止前端服务
print_info "停止前端服务..."
docker-compose stop frontend

# 方法1：直接在容器中安装依赖（快速修复）
print_info "尝试在现有容器中安装chart.js..."
if docker-compose exec frontend npm install chart.js@^4.4.0 2>/dev/null; then
    print_success "依赖安装成功！"
    
    # 重启前端服务
    print_info "重启前端服务..."
    docker-compose start frontend
    
    print_success "修复完成！请刷新浏览器页面"
    print_info "访问地址: http://localhost:3000"
    exit 0
fi

# 方法2：重新构建前端镜像
print_warning "容器内安装失败，尝试重新构建前端镜像..."

# 清理前端镜像
print_info "清理旧的前端镜像..."
docker-compose rm -f frontend
docker rmi chatdb-frontend 2>/dev/null || true

# 重新构建前端
print_info "重新构建前端镜像..."
docker-compose build --no-cache frontend

if [[ $? -eq 0 ]]; then
    print_success "前端镜像构建成功！"
    
    # 启动前端服务
    print_info "启动前端服务..."
    docker-compose up -d frontend
    
    # 等待服务启动
    print_info "等待前端服务启动..."
    sleep 15
    
    # 检查服务状态
    if docker-compose ps frontend | grep -q "Up"; then
        print_success "前端服务启动成功！"
        print_info "访问地址: http://localhost:3000"
        print_info "请等待1-2分钟让应用完全加载"
    else
        print_error "前端服务启动失败"
        print_info "查看日志: docker-compose logs frontend"
    fi
else
    print_error "前端镜像构建失败"
    
    # 方法3：手动修复
    print_info "尝试手动修复..."
    print_info "请执行以下命令："
    echo
    echo "1. 进入前端目录:"
    echo "   cd frontend"
    echo
    echo "2. 安装依赖:"
    echo "   npm install chart.js@^4.4.0"
    echo
    echo "3. 重新构建:"
    echo "   cd .."
    echo "   docker-compose build --no-cache frontend"
    echo "   docker-compose up -d frontend"
fi

echo
print_info "如果问题仍然存在，请检查:"
echo "1. package.json中是否包含chart.js依赖"
echo "2. Docker容器是否有足够的内存"
echo "3. 网络连接是否正常"
echo
print_info "查看详细日志: docker-compose logs -f frontend"
