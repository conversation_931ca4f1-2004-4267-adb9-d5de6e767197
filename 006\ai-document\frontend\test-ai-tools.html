<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI工具页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }
        
        .header {
            background: #fff;
            padding: 0 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            margin-right: 16px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
        }
        
        .back-btn:hover {
            background: #f5f5f5;
        }
        
        .breadcrumb {
            color: #8c8c8c;
            font-size: 14px;
        }
        
        .breadcrumb .active {
            color: #1890ff;
            font-weight: 500;
        }
        
        .close-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
        }
        
        .close-btn:hover {
            background: #f5f5f5;
        }
        
        .content {
            padding: 40px 24px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .title-section {
            text-align: center;
            margin: 80px 0;
        }

        .subtitle {
            font-size: 16px;
            color: #999;
        }

        .tools-container {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 32px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .tool-button {
            height: 48px;
            padding: 0 24px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            background: #fff;
            color: #262626;
            font-size: 14px;
            font-weight: normal;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .tool-button:hover {
            border-color: #1890ff;
            color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }

        .tool-icon {
            font-size: 16px;
        }
        

    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <button class="back-btn">←</button>
            <div class="breadcrumb">
                <span>开始</span> / <span>加入</span> / <span class="active">AI 妙笔AI</span>
            </div>
        </div>
        <button class="close-btn">×</button>
    </div>
    
    <div class="content">
        <div class="title-section">
            <p class="subtitle">请在此输入内容</p>
        </div>

        <div class="tools-container">
            <a href="#" class="tool-button" data-tool="ai_writer">
                <span class="tool-icon">🤖</span>
                AI写作
            </a>

            <a href="#" class="tool-button" data-tool="ai_polish">
                <span class="tool-icon">📄</span>
                AI对效
            </a>

            <a href="#" class="tool-button" data-tool="ai_color">
                <span class="tool-icon">🎨</span>
                AI协色
            </a>

            <a href="#" class="tool-button" data-tool="deepseek">
                <span class="tool-icon">🔍</span>
                deepseek
            </a>

            <a href="#" class="tool-button" data-tool="ai_compare">
                <span class="tool-icon">⚖️</span>
                AI对比
            </a>

            <a href="#" class="tool-button" data-tool="typesetting">
                <span class="tool-icon">📚</span>
                排版
            </a>
        </div>
    </div>
    
    <script>
        // 添加点击事件
        document.querySelectorAll('.tool-button').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const tool = button.getAttribute('data-tool');
                alert(`点击了 ${button.textContent.trim()} 工具`);
            });
        });
        
        // 返回按钮
        document.querySelector('.back-btn').addEventListener('click', () => {
            alert('返回首页');
        });
        
        // 关闭按钮
        document.querySelector('.close-btn').addEventListener('click', () => {
            alert('关闭页面');
        });
    </script>
</body>
</html>
