/* AI写作编辑器样式 */

/* 全屏模式 */
.ai-writing-editor-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #fff;
}

/* 编辑器容器 */
.ai-writing-editor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 工具栏样式 */
.ai-writing-toolbar {
  background: linear-gradient(to bottom, #fafafa, #f0f0f0);
  border-bottom: 1px solid #d9d9d9;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 60px;
}

.ai-writing-toolbar .ant-btn {
  border: none;
  background: transparent;
  color: #595959;
  font-size: 12px;
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  transition: all 0.2s;
}

.ai-writing-toolbar .ant-btn:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.ai-writing-toolbar .ant-btn-primary {
  background: #1890ff;
  color: #fff;
}

.ai-writing-toolbar .ant-btn-primary:hover {
  background: #40a9ff;
}

/* 编辑器主体 */
.ai-writing-editor-body {
  flex: 1;
  padding: 24px;
  background: #f8f9fa;
  display: flex;
  justify-content: center;
  overflow: auto;
}

.ai-writing-editor-body.fullscreen {
  padding: 0;
}

/* 文档容器 */
.ai-writing-document {
  width: 100%;
  max-width: 210mm;
  background: #fff;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 600px;
  transition: all 0.3s ease;
}

.ai-writing-document.fullscreen {
  max-width: 100%;
  box-shadow: none;
  border-radius: 0;
  height: 100%;
}

/* 标题区域 */
.ai-writing-title {
  padding: 24px 40px 16px 40px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.ai-writing-title .ant-input {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  border: none;
  box-shadow: none;
  background: transparent;
}

.ai-writing-title .ant-input:focus {
  border: none;
  box-shadow: none;
}

/* 编辑器区域 */
.ai-writing-editor-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* Quill编辑器自定义样式 */
.ai-writing-editor-area .ql-toolbar {
  background: linear-gradient(to bottom, #fafafa, #f0f0f0) !important;
  border: none !important;
  border-bottom: 1px solid #e8e8e8 !important;
  padding: 12px 16px !important;
}

.ai-writing-editor-area .ql-container {
  border: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
}

.ai-writing-editor-area .ql-editor {
  padding: 24px 40px !important;
  min-height: 400px;
  color: #262626;
}

.ai-writing-editor-area .ql-editor.ql-blank::before {
  color: #bfbfbf;
  font-style: normal;
  left: 40px;
}

/* 页脚信息 */
.ai-writing-footer {
  padding: 12px 40px;
  border-top: 1px solid #e8e8e8;
  background: linear-gradient(to right, #fafafa, #f5f5f5);
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 40px;
}

/* AI助手弹窗样式 */
.ai-assistant-popup {
  position: fixed;
  z-index: 9999;
  width: 320px;
  max-height: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  overflow: hidden;
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-assistant-popup .ant-list-item {
  transition: background-color 0.2s;
  cursor: pointer;
}

.ai-assistant-popup .ant-list-item:hover {
  background-color: #f0f8ff;
}

/* 主题选择模态框样式 */
.theme-selection-modal .ant-modal-body {
  padding: 16px 24px;
}

.theme-selection-modal .theme-category {
  margin-bottom: 24px;
}

.theme-selection-modal .theme-category-title {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.theme-selection-modal .theme-item {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.theme-selection-modal .theme-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transform: translateY(-2px);
}

.theme-selection-modal .theme-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.theme-selection-modal .theme-item-description {
  flex: 1;
  font-size: 12px;
  line-height: 1.5;
  color: #8c8c8c;
}

.theme-selection-modal .theme-item-badge {
  margin-top: 8px;
  padding: 4px 8px;
  background: #f0f8ff;
  border-radius: 4px;
  text-align: center;
}

/* AI配置模态框样式 */
.ai-config-modal .ant-modal-body {
  padding: 16px 24px;
}

.ai-config-modal .config-form {
  flex: 1;
  overflow-y: auto;
}

.ai-config-modal .result-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ai-config-modal .result-content {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
  overflow-y: auto;
  white-space: pre-wrap;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, monospace;
  min-height: 300px;
}

.ai-config-modal .result-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #e8e8e8;
  border-radius: 6px;
  color: #bfbfbf;
  text-align: center;
  min-height: 300px;
}

.ai-config-modal .loading-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-writing-toolbar {
    padding: 8px 12px;
    flex-wrap: wrap;
    min-height: auto;
  }
  
  .ai-writing-editor-body {
    padding: 12px;
  }
  
  .ai-writing-document {
    border-radius: 4px;
  }
  
  .ai-writing-title {
    padding: 16px 20px 12px 20px;
  }
  
  .ai-writing-editor-area .ql-editor {
    padding: 16px 20px !important;
  }
  
  .ai-writing-footer {
    padding: 8px 20px;
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
  
  .ai-assistant-popup {
    width: 280px;
    max-height: 300px;
  }
  
  .ai-config-modal .ant-modal {
    width: 95% !important;
    max-width: none !important;
  }
}

/* 打印样式 */
@media print {
  .ai-writing-toolbar,
  .ai-writing-footer,
  .ai-assistant-popup {
    display: none !important;
  }
  
  .ai-writing-editor-body {
    padding: 0;
    background: #fff;
  }
  
  .ai-writing-document {
    box-shadow: none;
    border-radius: 0;
    max-width: none;
  }
  
  .ai-writing-editor-area .ql-toolbar {
    display: none !important;
  }
  
  .ai-writing-editor-area .ql-container {
    border: none !important;
  }
  
  .ai-writing-editor-area .ql-editor {
    padding: 20px !important;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .ai-writing-editor-container {
    background: #141414;
    color: #fff;
  }
  
  .ai-writing-toolbar {
    background: linear-gradient(to bottom, #262626, #1f1f1f);
    border-bottom-color: #434343;
  }
  
  .ai-writing-editor-body {
    background: #141414;
  }
  
  .ai-writing-document {
    background: #1f1f1f;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  }
  
  .ai-writing-title {
    background: #1f1f1f;
    border-bottom-color: #434343;
  }
  
  .ai-writing-footer {
    background: linear-gradient(to right, #262626, #1f1f1f);
    border-top-color: #434343;
    color: #bfbfbf;
  }
  
  .ai-assistant-popup {
    background: #1f1f1f;
    border-color: #434343;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  }
}
