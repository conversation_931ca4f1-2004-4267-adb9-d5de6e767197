@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: AI智能应用平台一键部署脚本 (Windows版本)
:: 适用于Windows系统的零基础用户

echo ========================================
echo    AI智能应用平台一键部署脚本
echo ========================================
echo.

:: 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Docker未安装！
    echo 请先安装Docker Desktop for Windows
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

:: 检查Docker Compose是否可用
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Docker Compose不可用！
    echo 请确保Docker Desktop正在运行
    pause
    exit /b 1
)

echo [信息] Docker环境检查通过

:: 获取API密钥
echo.
echo 请输入您的OpenAI API密钥（必需）：
echo 格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
set /p OPENAI_API_KEY="OpenAI API Key: "

if "!OPENAI_API_KEY!"=="" (
    echo [错误] OpenAI API密钥不能为空！
    pause
    exit /b 1
)

echo.
echo 请输入您的DeepSeek API密钥（可选，直接回车跳过）：
set /p DEEPSEEK_API_KEY="DeepSeek API Key: "

:: 选择部署方案
echo.
echo 请选择部署方案：
echo 1) ChatDB - Text2SQL智能问答系统（推荐新手）
echo 2) AnythingChat - 高级RAG和多智能体系统
echo 3) Super Memory - 智能记忆管理系统
echo.
set /p choice="请输入选择 (1-3): "

if "!choice!"=="1" (
    set DEPLOY_TARGET=chatdb
    echo [信息] 选择部署：ChatDB
) else if "!choice!"=="2" (
    set DEPLOY_TARGET=anythingchat
    echo [信息] 选择部署：AnythingChat
) else if "!choice!"=="3" (
    set DEPLOY_TARGET=supermemory
    echo [信息] 选择部署：Super Memory
) else (
    echo [错误] 无效选择！
    pause
    exit /b 1
)

:: 部署ChatDB
if "!DEPLOY_TARGET!"=="chatdb" (
    echo.
    echo [信息] 部署ChatDB应用...
    
    if not exist "chatdb" (
        echo [错误] 未找到chatdb目录
        pause
        exit /b 1
    )
    
    cd chatdb
    
    :: 创建环境变量文件
    (
        echo OPENAI_API_KEY=!OPENAI_API_KEY!
        echo MYSQL_ROOT_PASSWORD=password
        echo MYSQL_DATABASE=chatdb
        echo NEO4J_AUTH=neo4j/password
        echo REACT_APP_API_URL=http://localhost:8000/api
    ) > .env
    
    :: 启动服务
    echo [信息] 启动Docker服务...
    docker-compose up -d
    
    if errorlevel 1 (
        echo [错误] 服务启动失败！
        pause
        exit /b 1
    )
    
    :: 等待服务启动
    echo [信息] 等待服务启动...
    timeout /t 30 /nobreak >nul
    
    :: 初始化数据库
    echo [信息] 初始化数据库...
    docker-compose exec -T backend python init_db.py
    
    echo.
    echo [成功] ChatDB部署完成！
    echo 访问地址：
    echo   前端界面: http://localhost:3000
    echo   后端API: http://localhost:8000/docs
    echo   Neo4j管理: http://localhost:7474 ^(用户名:neo4j 密码:password^)
    
    cd ..
)

:: 部署AnythingChat
if "!DEPLOY_TARGET!"=="anythingchat" (
    echo.
    echo [信息] 部署AnythingChat应用...
    
    if not exist "anythingchat\backend\docker" (
        echo [错误] 未找到anythingchat目录
        pause
        exit /b 1
    )
    
    cd anythingchat\backend\docker
    
    :: 更新环境变量文件
    if exist "env\r2r.env" (
        powershell -Command "(Get-Content 'env\r2r.env') -replace '#OPENAI_API_KEY=', 'OPENAI_API_KEY=!OPENAI_API_KEY!' | Set-Content 'env\r2r.env'"
        if not "!DEEPSEEK_API_KEY!"=="" (
            powershell -Command "(Get-Content 'env\r2r.env') -replace 'DEEPSEEK_API_KEY=.*', 'DEEPSEEK_API_KEY=!DEEPSEEK_API_KEY!' | Set-Content 'env\r2r.env'"
        )
    )
    
    :: 启动基础服务
    echo [信息] 启动基础服务...
    docker-compose up -d postgres minio graph_clustering
    
    if errorlevel 1 (
        echo [错误] 基础服务启动失败！
        pause
        exit /b 1
    )
    
    :: 等待基础服务启动
    echo [信息] 等待基础服务启动...
    timeout /t 45 /nobreak >nul
    
    :: 启动主应用
    echo [信息] 启动主应用...
    docker-compose up -d r2r r2r-dashboard
    
    if errorlevel 1 (
        echo [错误] 主应用启动失败！
        pause
        exit /b 1
    )
    
    echo.
    echo [成功] AnythingChat部署完成！
    echo 访问地址：
    echo   R2R Dashboard: http://localhost:7273
    echo   R2R API: http://localhost:7272
    echo   MinIO管理: http://localhost:9001 ^(用户名:minioadmin 密码:minioadmin^)
    
    cd ..\..\..
)

:: 部署Super Memory
if "!DEPLOY_TARGET!"=="supermemory" (
    echo.
    echo [信息] 部署Super Memory应用...
    
    if not exist "super-memory\server" (
        echo [错误] 未找到super-memory目录
        pause
        exit /b 1
    )
    
    cd super-memory\server
    
    :: 创建环境变量文件
    (
        echo OPENAI_API_KEY=!OPENAI_API_KEY!
        echo POSTGRES_USER=postgres
        echo POSTGRES_PASSWORD=postgres
        echo NEO4J_AUTH=neo4j/mem0graph
        echo QDRANT_URL=http://localhost:6333
    ) > .env
    
    :: 启动服务
    echo [信息] 启动Docker服务...
    docker-compose up -d
    
    if errorlevel 1 (
        echo [错误] 服务启动失败！
        pause
        exit /b 1
    )
    
    echo.
    echo [成功] Super Memory部署完成！
    echo 访问地址：
    echo   Memory API: http://localhost:8888
    echo   Neo4j管理: http://localhost:8474 ^(用户名:neo4j 密码:mem0graph^)
    
    cd ..\..
)

echo.
echo [成功] 部署完成！
echo.
echo 如需查看服务状态，请运行：docker ps
echo 如需查看日志，请运行：docker-compose logs -f
echo 详细使用说明请参考：部署操作文档.md
echo.
echo 按任意键退出...
pause >nul
