#!/bin/bash

# AI智能应用平台一键部署脚本
# 适用于Ubuntu系统的零基础用户

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "请不要使用root用户运行此脚本！"
        print_info "请使用普通用户账号运行：bash quick-deploy.sh"
        exit 1
    fi
}

# 检查Docker权限
check_docker_permissions() {
    print_info "检查Docker权限..."

    if ! docker ps &> /dev/null; then
        print_warning "Docker权限问题检测到"
        print_info "尝试自动修复Docker权限..."

        if [[ -f "./fix-docker-permissions.sh" ]]; then
            chmod +x ./fix-docker-permissions.sh
            ./fix-docker-permissions.sh

            # 再次测试
            if docker ps &> /dev/null; then
                print_success "Docker权限修复成功！"
                return 0
            fi
        fi

        print_error "Docker权限问题未解决"
        print_info "请手动执行以下命令："
        echo "  sudo usermod -aG docker \$USER"
        echo "  newgrp docker"
        echo "  或者重新登录系统"
        exit 1
    fi

    print_success "Docker权限检查通过"
}

# 检查系统要求
check_system() {
    print_info "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        print_error "无法检测操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        print_warning "此脚本专为Ubuntu设计，当前系统：$ID"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [[ $total_mem -lt 4 ]]; then
        print_warning "系统内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 10485760 ]]; then  # 10GB in KB
        print_warning "可用磁盘空间少于10GB，可能不足"
    fi
    
    print_success "系统检查完成"
}

# 安装Docker
install_docker() {
    if command -v docker &> /dev/null; then
        print_info "Docker已安装，版本：$(docker --version)"
        return
    fi
    
    print_info "安装Docker..."
    
    # 更新软件包列表
    sudo apt update
    
    # 安装必要的软件包
    sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 更新软件包列表
    sudo apt update
    
    # 安装Docker
    sudo apt install -y docker-ce docker-ce-cli containerd.io
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    print_success "Docker安装完成"
    print_warning "请重新登录或运行 'newgrp docker' 使Docker组权限生效"
}

# 安装Docker Compose
install_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        print_info "Docker Compose已安装，版本：$(docker-compose --version)"
        return
    fi
    
    print_info "安装Docker Compose..."
    
    # 下载Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 添加执行权限
    sudo chmod +x /usr/local/bin/docker-compose
    
    print_success "Docker Compose安装完成"
}

# 配置API密钥
configure_api_keys() {
    print_info "配置API密钥..."
    
    echo "请输入您的OpenAI API密钥（必需）："
    echo "格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    read -p "OpenAI API Key: " openai_key
    
    if [[ -z "$openai_key" ]]; then
        print_error "OpenAI API密钥不能为空！"
        exit 1
    fi
    
    echo "请输入您的DeepSeek API密钥（可选，直接回车跳过）："
    read -p "DeepSeek API Key: " deepseek_key
    
    # 保存API密钥到环境变量文件
    export OPENAI_API_KEY="$openai_key"
    export DEEPSEEK_API_KEY="$deepseek_key"
    
    print_success "API密钥配置完成"
}

# 选择部署方案
choose_deployment() {
    print_info "请选择部署方案："
    echo "1) ChatDB - Text2SQL智能问答系统（推荐新手）"
    echo "2) AnythingChat - 高级RAG和多智能体系统"
    echo "3) Super Memory - 智能记忆管理系统"
    echo "4) 全部部署（需要更多资源）"
    
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            DEPLOY_TARGET="chatdb"
            print_info "选择部署：ChatDB"
            ;;
        2)
            DEPLOY_TARGET="anythingchat"
            print_info "选择部署：AnythingChat"
            ;;
        3)
            DEPLOY_TARGET="supermemory"
            print_info "选择部署：Super Memory"
            ;;
        4)
            DEPLOY_TARGET="all"
            print_info "选择部署：全部应用"
            ;;
        *)
            print_error "无效选择！"
            exit 1
            ;;
    esac
}

# 部署ChatDB
deploy_chatdb() {
    print_info "部署ChatDB应用..."
    
    cd chatdb
    
    # 创建环境变量文件
    cat > .env << EOF
OPENAI_API_KEY=$OPENAI_API_KEY
MYSQL_ROOT_PASSWORD=password
MYSQL_DATABASE=chatdb
NEO4J_AUTH=neo4j/password
REACT_APP_API_URL=http://localhost:8000/api
EOF
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 30
    
    # 初始化数据库
    docker-compose exec -T backend python init_db.py || true
    
    print_success "ChatDB部署完成！"
    print_info "访问地址："
    print_info "  前端界面: http://localhost:3000"
    print_info "  后端API: http://localhost:8000/docs"
    print_info "  Neo4j管理: http://localhost:7474 (用户名:neo4j 密码:password)"
    
    cd ..
}

# 部署AnythingChat
deploy_anythingchat() {
    print_info "部署AnythingChat应用..."
    
    cd anythingchat/backend/docker
    
    # 更新环境变量文件
    sed -i "s/#OPENAI_API_KEY=/OPENAI_API_KEY=$OPENAI_API_KEY/" env/r2r.env
    if [[ -n "$DEEPSEEK_API_KEY" ]]; then
        sed -i "s/DEEPSEEK_API_KEY=.*/DEEPSEEK_API_KEY=$DEEPSEEK_API_KEY/" env/r2r.env
    fi
    
    # 启动基础服务
    docker-compose up -d postgres minio graph_clustering
    
    # 等待基础服务启动
    print_info "等待基础服务启动..."
    sleep 45
    
    # 启动主应用
    docker-compose up -d r2r r2r-dashboard
    
    print_success "AnythingChat部署完成！"
    print_info "访问地址："
    print_info "  R2R Dashboard: http://localhost:7273"
    print_info "  R2R API: http://localhost:7272"
    print_info "  MinIO管理: http://localhost:9001 (用户名:minioadmin 密码:minioadmin)"
    
    cd ../../..
}

# 部署Super Memory
deploy_supermemory() {
    print_info "部署Super Memory应用..."
    
    cd super-memory/server
    
    # 创建环境变量文件
    cat > .env << EOF
OPENAI_API_KEY=$OPENAI_API_KEY
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
NEO4J_AUTH=neo4j/mem0graph
QDRANT_URL=http://localhost:6333
EOF
    
    # 启动服务
    docker-compose up -d
    
    print_success "Super Memory部署完成！"
    print_info "访问地址："
    print_info "  Memory API: http://localhost:8888"
    print_info "  Neo4j管理: http://localhost:8474 (用户名:neo4j 密码:mem0graph)"
    
    cd ../..
}

# 主函数
main() {
    echo "========================================"
    echo "   AI智能应用平台一键部署脚本"
    echo "========================================"
    echo
    
    check_root
    check_system
    install_docker
    install_docker_compose
    check_docker_permissions
    configure_api_keys
    choose_deployment
    
    # 确保在正确的目录
    if [[ ! -d "chatdb" && ! -d "anythingchat" && ! -d "super-memory" ]]; then
        print_error "未找到应用目录，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    case $DEPLOY_TARGET in
        "chatdb")
            deploy_chatdb
            ;;
        "anythingchat")
            deploy_anythingchat
            ;;
        "supermemory")
            deploy_supermemory
            ;;
        "all")
            deploy_chatdb
            deploy_anythingchat
            deploy_supermemory
            ;;
    esac
    
    echo
    print_success "部署完成！"
    print_info "如需查看服务状态，请运行：docker ps"
    print_info "如需查看日志，请运行：docker-compose logs -f"
    print_info "详细使用说明请参考：部署操作文档.md"
}

# 运行主函数
main "$@"
