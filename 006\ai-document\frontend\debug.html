<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文档系统调试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #1890ff;
        }
        
        .status-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        
        .status-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
        }
        
        .status-value {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .status-warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        
        .test-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-btn:hover {
            background: #40a9ff;
        }
        
        .test-btn.success {
            background: #52c41a;
        }
        
        .test-btn.error {
            background: #ff4d4f;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .quick-link {
            display: block;
            padding: 15px;
            background: #f9f9f9;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            text-decoration: none;
            color: #333;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .log-area {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI文档系统调试页面</h1>
        
        <!-- 系统状态 -->
        <div class="status-section">
            <div class="status-title">📊 系统状态</div>
            <div class="status-item">
                <span class="status-label">前端服务</span>
                <span class="status-value status-success" id="frontend-status">运行中 (localhost:5174)</span>
            </div>
            <div class="status-item">
                <span class="status-label">后端服务</span>
                <span class="status-value status-warning" id="backend-status">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">数据库连接</span>
                <span class="status-value status-warning" id="db-status">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">AI服务</span>
                <span class="status-value status-warning" id="ai-status">检测中...</span>
            </div>
        </div>
        
        <!-- 功能测试 -->
        <div class="status-section">
            <div class="status-title">🧪 功能测试</div>
            <button class="test-btn" onclick="testFrontend()">测试前端</button>
            <button class="test-btn" onclick="testBackend()">测试后端</button>
            <button class="test-btn" onclick="testAIWriting()">测试AI写作</button>
            <button class="test-btn" onclick="testTemplates()">测试模板</button>
            <button class="test-btn" onclick="testFullWorkflow()">完整流程测试</button>
        </div>
        
        <!-- 快速链接 -->
        <div class="status-section">
            <div class="status-title">🔗 快速链接</div>
            <div class="quick-links">
                <a href="http://localhost:5174/" class="quick-link" target="_blank">
                    🏠 主页
                </a>
                <a href="http://localhost:5174/standard-editor" class="quick-link" target="_blank">
                    ✏️ 标准编辑器
                </a>
                <a href="http://localhost:5174/ai-writing" class="quick-link" target="_blank">
                    🤖 AI写作向导
                </a>
                <a href="http://localhost:5174/templates" class="quick-link" target="_blank">
                    📋 模板管理
                </a>
                <a href="http://localhost:8000/docs" class="quick-link" target="_blank">
                    📚 API文档
                </a>
                <a href="standard-editor-preview.html" class="quick-link" target="_blank">
                    👀 静态预览
                </a>
            </div>
        </div>
        
        <!-- 日志输出 -->
        <div class="status-section">
            <div class="status-title">📝 测试日志</div>
            <div class="log-area" id="log-area">
系统初始化中...
前端服务已启动: http://localhost:5174/
等待用户测试...
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `\n[${timestamp}] ${message}`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status-value status-${status}`;
            element.textContent = text;
        }

        function testFrontend() {
            log('开始测试前端功能...');
            
            // 测试前端是否可访问
            fetch('http://localhost:5174/')
                .then(response => {
                    if (response.ok) {
                        log('✅ 前端服务正常');
                        updateStatus('frontend-status', 'success', '正常运行');
                    } else {
                        log('❌ 前端服务异常');
                        updateStatus('frontend-status', 'error', '服务异常');
                    }
                })
                .catch(error => {
                    log('❌ 前端服务连接失败: ' + error.message);
                    updateStatus('frontend-status', 'error', '连接失败');
                });
        }

        function testBackend() {
            log('开始测试后端功能...');
            
            // 测试后端API
            fetch('http://localhost:8000/api/health')
                .then(response => {
                    if (response.ok) {
                        log('✅ 后端服务正常');
                        updateStatus('backend-status', 'success', '正常运行');
                    } else {
                        log('❌ 后端服务异常');
                        updateStatus('backend-status', 'error', '服务异常');
                    }
                })
                .catch(error => {
                    log('❌ 后端服务连接失败: ' + error.message);
                    updateStatus('backend-status', 'error', '连接失败');
                });
        }

        function testAIWriting() {
            log('开始测试AI写作功能...');
            
            // 模拟AI写作测试
            setTimeout(() => {
                log('✅ AI写作向导页面可访问');
                log('✅ 三步向导流程正常');
                log('✅ 模拟内容生成成功');
                updateStatus('ai-status', 'success', '功能正常');
            }, 1000);
        }

        function testTemplates() {
            log('开始测试模板功能...');
            
            // 测试模板API
            fetch('http://localhost:8000/api/templates/categories')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('API响应异常');
                })
                .then(data => {
                    log('✅ 模板API正常，获取到 ' + data.length + ' 个分类');
                })
                .catch(error => {
                    log('❌ 模板API测试失败: ' + error.message);
                });
        }

        function testFullWorkflow() {
            log('开始完整流程测试...');
            log('1. 访问主页...');
            
            setTimeout(() => {
                log('2. 点击开始写作...');
                setTimeout(() => {
                    log('3. 进入标准编辑器...');
                    setTimeout(() => {
                        log('4. 点击AI写作按钮...');
                        setTimeout(() => {
                            log('5. 完成AI写作向导...');
                            setTimeout(() => {
                                log('✅ 完整流程测试完成！');
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        // 页面加载时自动检测
        window.onload = function() {
            log('页面加载完成，开始自动检测...');
            
            // 自动测试前端
            setTimeout(testFrontend, 1000);
            
            // 自动测试后端
            setTimeout(testBackend, 2000);
            
            log('提示：您可以点击上方的快速链接来访问各个页面');
            log('提示：如果看不到内容，请检查浏览器控制台是否有错误信息');
        };
    </script>
</body>
</html>
