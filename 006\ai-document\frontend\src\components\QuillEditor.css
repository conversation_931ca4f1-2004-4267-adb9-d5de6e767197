/* 终极简化版 - Quill编辑器样式 */

/* 容器样式 */
.quill-editor-container {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  min-height: 400px !important;
  background: #fff !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
}

/* 工具栏样式 - 终极保护 */
.quill-editor-container .ql-toolbar {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 42px !important;
  background: linear-gradient(to bottom, #fafafa, #f0f0f0) !important;
  border: none !important;
  border-bottom: 1px solid #e8e8e8 !important;
  padding: 12px 16px !important;
  position: relative !important;
  z-index: 1000 !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
}

/* 工具栏按钮样式 */
.quill-editor-container .ql-toolbar button,
.quill-editor-container .ql-toolbar .ql-picker {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 编辑器内容区域 */
.quill-editor-container .ql-container {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  border: none !important;
}

.quill-editor-container .ql-editor {
  flex: 1 !important;
  padding: 24px 40px !important;
  color: #262626 !important;
  background: #fff !important;
  font-family: "Microsoft YaHei", "微软雅黑", sans-serif !important;
  font-size: 14px !important;
  line-height: 1.8 !important;
  overflow-y: auto !important;
}

/* 占位符样式 */
.quill-editor-container .ql-editor.ql-blank::before {
  color: #bfbfbf !important;
  font-style: italic !important;
  left: 40px !important;
  right: 40px !important;
}

/* 基本样式 */
.quill-editor-container .ql-toolbar button:hover {
  background: #e6f7ff !important;
  color: #1890ff !important;
}

.quill-editor-container .ql-toolbar button.ql-active {
  background: #1890ff !important;
  color: #fff !important;
}

/* 这是一个极简版的CSS文件，只保留最基本的样式 */
