/* 富文本编辑器样式 */

/* 编辑器内容样式 */
.rich-editor {
  font-family: "Microsoft YaHei", "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 标题样式 */
.rich-editor h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 24px 0 16px 0;
  color: #262626;
  line-height: 1.4;
  text-align: center;
}

.rich-editor h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0 12px 0;
  color: #262626;
  line-height: 1.4;
}

.rich-editor h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 16px 0 8px 0;
  color: #262626;
  line-height: 1.4;
}

.rich-editor h4 {
  font-size: 18px;
  font-weight: bold;
  margin: 12px 0 6px 0;
  color: #262626;
  line-height: 1.4;
}

.rich-editor h5 {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0 4px 0;
  color: #262626;
  line-height: 1.4;
}

.rich-editor h6 {
  font-size: 14px;
  font-weight: bold;
  margin: 8px 0 2px 0;
  color: #262626;
  line-height: 1.4;
}

/* 段落样式 */
.rich-editor p {
  margin: 12px 0;
  line-height: 1.8;
  text-align: justify;
  text-indent: 2em; /* 首行缩进 */
}

/* 列表样式 */
.rich-editor ul {
  margin: 12px 0;
  padding-left: 24px;
}

.rich-editor ol {
  margin: 12px 0;
  padding-left: 24px;
}

.rich-editor li {
  margin: 6px 0;
  line-height: 1.6;
}

/* 链接样式 */
.rich-editor a {
  color: #1890ff;
  text-decoration: underline;
}

.rich-editor a:hover {
  color: #40a9ff;
}

/* 强调样式 */
.rich-editor strong {
  font-weight: bold;
}

.rich-editor em {
  font-style: italic;
}

.rich-editor u {
  text-decoration: underline;
}

.rich-editor s {
  text-decoration: line-through;
}

/* 引用样式 */
.rich-editor blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #1890ff;
  background: #f6f8fa;
  color: #666;
  font-style: italic;
}

/* 代码样式 */
.rich-editor code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "Courier New", Courier, monospace;
  font-size: 0.9em;
}

.rich-editor pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 12px 0;
}

.rich-editor pre code {
  background: none;
  padding: 0;
}

/* 表格样式 */
.rich-editor table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.rich-editor th,
.rich-editor td {
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  text-align: left;
}

.rich-editor th {
  background: #fafafa;
  font-weight: bold;
}

/* 分割线样式 */
.rich-editor hr {
  border: none;
  border-top: 1px solid #d9d9d9;
  margin: 24px 0;
}

/* 图片样式 */
.rich-editor img {
  max-width: 100%;
  height: auto;
  margin: 12px 0;
  border-radius: 4px;
}

/* 选中文本样式 */
.rich-editor ::selection {
  background: #bae7ff;
}

/* 工具栏按钮激活状态 */
.toolbar-button-active {
  background: #e6f7ff !important;
  color: #1890ff !important;
  border-color: #91d5ff !important;
}

/* 工具栏样式增强 */
.editor-toolbar {
  background: linear-gradient(to bottom, #fafafa, #f0f0f0);
  border-bottom: 1px solid #d9d9d9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.editor-toolbar .ant-btn {
  border: none;
  background: transparent;
  transition: all 0.2s;
}

.editor-toolbar .ant-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.editor-toolbar .ant-divider {
  border-color: #d9d9d9;
}

/* 文档容器样式 */
.document-container {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.3s ease;
}

.document-container:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.16);
}

/* 页脚样式 */
.document-footer {
  background: linear-gradient(to right, #fafafa, #f5f5f5);
  border-top: 1px solid #e8e8e8;
}

/* 全屏模式样式 */
.fullscreen-editor {
  background: #fff !important;
}

.fullscreen-editor .document-container {
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rich-editor {
    font-size: 16px;
  }
  
  .rich-editor h1 {
    font-size: 24px;
  }
  
  .rich-editor h2 {
    font-size: 20px;
  }
  
  .rich-editor h3 {
    font-size: 18px;
  }
  
  .rich-editor p {
    text-indent: 1.5em;
  }
  
  .document-container {
    margin: 0 !important;
    border-radius: 0 !important;
  }
}

/* 打印样式 */
@media print {
  .editor-toolbar,
  .document-footer,
  .ant-btn {
    display: none !important;
  }
  
  .document-container {
    box-shadow: none !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .rich-editor {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .rich-editor h1 {
    font-size: 18pt;
  }
  
  .rich-editor h2 {
    font-size: 16pt;
  }
  
  .rich-editor h3 {
    font-size: 14pt;
  }
}
