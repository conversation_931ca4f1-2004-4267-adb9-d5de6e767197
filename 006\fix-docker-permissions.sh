#!/bin/bash

# Docker权限修复脚本
# 解决Docker权限问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

echo "========================================"
echo "   Docker权限修复脚本"
echo "========================================"
echo

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    print_error "Docker未安装！请先安装Docker"
    exit 1
fi

print_info "检查Docker服务状态..."

# 检查Docker服务是否运行
if ! sudo systemctl is-active --quiet docker; then
    print_info "启动Docker服务..."
    sudo systemctl start docker
    sudo systemctl enable docker
    print_success "Docker服务已启动"
else
    print_success "Docker服务正在运行"
fi

# 检查当前用户是否在docker组中
if groups $USER | grep -q docker; then
    print_info "用户 $USER 已在docker组中"
else
    print_info "将用户 $USER 添加到docker组..."
    sudo usermod -aG docker $USER
    print_success "用户已添加到docker组"
fi

# 检查Docker socket权限
print_info "检查Docker socket权限..."
if [[ -S /var/run/docker.sock ]]; then
    print_success "Docker socket存在"
    
    # 临时修改socket权限（仅用于测试）
    print_info "临时修改Docker socket权限..."
    sudo chmod 666 /var/run/docker.sock
    
    # 测试Docker连接
    print_info "测试Docker连接..."
    if docker ps &> /dev/null; then
        print_success "Docker连接测试成功！"
        
        # 恢复正确的权限设置
        print_info "恢复Docker socket权限..."
        sudo chmod 660 /var/run/docker.sock
        sudo chown root:docker /var/run/docker.sock
        
        print_success "权限修复完成！"
        echo
        print_info "现在可以运行部署脚本了："
        print_info "  ./quick-deploy.sh"
        
    else
        print_error "Docker连接测试失败"
        print_info "请尝试以下解决方案："
        echo "1. 重新登录系统"
        echo "2. 或运行: newgrp docker"
        echo "3. 或重启Docker服务: sudo systemctl restart docker"
    fi
else
    print_error "Docker socket不存在，请检查Docker安装"
fi

echo
print_info "如果问题仍然存在，请尝试："
echo "1. 完全重启系统"
echo "2. 重新安装Docker"
echo "3. 检查系统日志: sudo journalctl -u docker"
