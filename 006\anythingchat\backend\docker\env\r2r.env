# R2R
R2R_PORT=7272
R2R_HOST=0.0.0.0
R2R_LOG_LEVEL=INFO
R2R_CONFIG_NAME=
R2R_CONFIG_PATH=/app/user_configs/r2r.toml
R2R_PROJECT_NAME=danwen
R2R_SECRET_KEY=
R2R_USER_TOOLS_PATH=/app/user_tools
R2R_LOG_FORMAT=

# Postgres Configuration
R2R_POSTGRES_USER=postgres
R2R_POSTGRES_PASSWORD=postgres
R2R_POSTGRES_HOST=postgres
R2R_POSTGRES_PORT=5432
R2R_POSTGRES_DBNAME=postgres
R2R_POSTGRES_MAX_CONNECTIONS=1024
R2R_POSTGRES_STATEMENT_CACHE_SIZE=100

# Hatchet
HATCHET_CLIENT_TLS_STRATEGY=none

# OpenAI
#OPENAI_API_KEY=
#OPENAI_API_BASE=

DEEPSEEK_API_KEY=***********************************

# Azure Foundry
AZURE_FOUNDRY_API_ENDPOINT=
AZURE_FOUNDRY_API_KEY=

# XAI / GROK
XAI_API_KEY=

# Anthropic
ANTHROPIC_API_KEY=

# Azure
AZURE_API_KEY=
AZURE_API_BASE=
AZURE_API_VERSION=

# Google Vertex AI
GOOGLE_APPLICATION_CREDENTIALS=
VERTEX_PROJECT=
VERTEX_LOCATION=

# Google Gemini
GEMINI_API_KEY=

# Mistral
MISTRAL_API_KEY=

# AWS Bedrock
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

# Groq
GROQ_API_KEY=

# Cohere
COHERE_API_KEY=

# Anyscale
ANYSCALE_API_KEY=

# Ollama如果不是安装在docker中，请设置ollama服务器的实际地址
OLLAMA_API_BASE=http://host.docker.internal:11434

# LM Studio
LM_STUDIO_API_BASE=http://host.docker.internal:1234
LM_STUDIO_API_KEY=1234

# Huggingface
HUGGINGFACE_API_BASE=http://host.docker.internal:8080
HUGGINGFACE_API_KEY=

# Unstructured
UNSTRUCTURED_API_KEY=
UNSTRUCTURED_API_URL=https://api.unstructured.io/general/v0/general
UNSTRUCTURED_SERVICE_URL=http://unstructured:7275
UNSTRUCTURED_NUM_WORKERS=10

# Graphologic
CLUSTERING_SERVICE_URL=http://graph_clustering:7276

# OAuth Credentials
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=

GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GITHUB_REDIRECT_URI=

# Email
MAILERSEND_API_KEY=
SENDGRID_API_KEY=

# Websearch
FIRECRAWL_API_KEY=
SERPER_API_KEY=
TAVILY_API_KEY=

# Sentry Tracing
R2R_SENTRY_DSN=
R2R_SENTRY_ENVIRONMENT=
R2R_SENTRY_TRACES_SAMPLE_RATE=
R2R_SENTRY_PROFILES_SAMPLE_RATE=
