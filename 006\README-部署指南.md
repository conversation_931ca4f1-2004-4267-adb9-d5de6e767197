# 🚀 AI智能应用平台 - 快速部署指南

欢迎使用AI智能应用平台！本指南将帮助您在Ubuntu系统上快速部署和运行我们的AI应用。

## 📋 快速开始（3步部署）

### 🐧 Linux/Ubuntu 用户

#### 第1步：环境检查
```bash
# 下载并运行环境检查脚本
chmod +x check-environment.sh
./check-environment.sh
```

#### 第2步：一键部署
```bash
# 运行一键部署脚本
chmod +x quick-deploy.sh
./quick-deploy.sh
```

### 🪟 Windows 用户

#### 第1步：环境检查
```cmd
# 双击运行或在命令行中执行
check-environment.bat
```

#### 第2步：一键部署
```cmd
# 双击运行或在命令行中执行
quick-deploy.bat
```

### 第3步：访问应用
根据您选择的部署方案，访问对应的地址：

**ChatDB（Text2SQL智能问答）**
- 🌐 前端界面：http://localhost:3000
- 📡 后端API：http://localhost:8000/docs
- 🗄️ Neo4j管理：http://localhost:7474

**AnythingChat（高级RAG系统）**
- 🌐 R2R Dashboard：http://localhost:7273
- 📡 R2R API：http://localhost:7272
- 📦 MinIO管理：http://localhost:9001

**Super Memory（智能记忆管理）**
- 📡 Memory API：http://localhost:8888
- 🗄️ Neo4j管理：http://localhost:8474

---

## 🎯 应用介绍

### 🤖 ChatDB - Text2SQL智能问答系统
**适合人群**：数据分析师、业务人员、初学者

**主要功能**：
- 🗣️ 自然语言转SQL查询
- 📊 数据库schema可视化管理
- 🔗 多数据库连接支持
- 📈 查询结果可视化

**使用场景**：
- 业务人员快速查询数据
- 数据分析师提高工作效率
- 学习SQL和数据库操作

### 🧠 AnythingChat - 高级RAG和多智能体系统
**适合人群**：开发者、研究人员、高级用户

**主要功能**：
- 📚 文档智能问答（RAG）
- 🤖 多智能体协作
- 🔍 混合检索（向量+关键词）
- 🌐 多模型支持（OpenAI、DeepSeek等）

**使用场景**：
- 企业知识库问答
- 文档智能分析
- 复杂任务自动化

### 🧠 Super Memory - 智能记忆管理系统
**适合人群**：知识工作者、研究人员

**主要功能**：
- 🧠 智能记忆存储和检索
- 🔗 知识图谱构建
- 📝 上下文感知记忆
- 🔄 记忆更新和演化

**使用场景**：
- 个人知识管理
- 长期对话记忆
- 智能助手增强

---

## 📚 详细文档

如需了解更多详细信息，请查看：
- 📖 [完整部署操作文档](./部署操作文档.md) - 详细的部署说明和故障排除

**Linux/Ubuntu 用户：**
- 🔧 [环境检查脚本](./check-environment.sh) - 检查系统是否满足部署要求
- ⚡ [一键部署脚本](./quick-deploy.sh) - 自动化部署脚本

**Windows 用户：**
- 🔧 [环境检查脚本](./check-environment.bat) - 检查系统是否满足部署要求
- ⚡ [一键部署脚本](./quick-deploy.bat) - 自动化部署脚本

---

## 🔑 准备工作

### 必需的API密钥
1. **OpenAI API密钥**（必需）
   - 访问：https://platform.openai.com/
   - 注册账号并创建API密钥
   - 格式：`sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

2. **DeepSeek API密钥**（可选，推荐）
   - 访问：https://platform.deepseek.com/
   - 注册账号并创建API密钥
   - 国产模型，性价比高

### 系统要求
- **操作系统**：
  - Ubuntu 20.04 LTS 或更高版本
  - Windows 10/11 (需安装Docker Desktop)
- **CPU**：4核心以上
- **内存**：8GB以上（最低4GB）
- **硬盘**：50GB以上可用空间
- **网络**：稳定的互联网连接

---

## 🛠️ 手动部署（高级用户）

如果您希望手动控制部署过程，可以按以下步骤操作：

### 1. 安装Docker和Docker Compose
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录使权限生效
newgrp docker
```

### 2. 配置环境变量
```bash
# 进入应用目录（以ChatDB为例）
cd chatdb

# 创建环境变量文件
cat > .env << EOF
OPENAI_API_KEY=your_openai_api_key_here
MYSQL_ROOT_PASSWORD=password
MYSQL_DATABASE=chatdb
NEO4J_AUTH=neo4j/password
REACT_APP_API_URL=http://localhost:8000/api
EOF
```

### 3. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

---

## 🔍 故障排除

### 常见问题

**问题1：Docker权限错误**
```bash
# 解决方案
sudo usermod -aG docker $USER
newgrp docker
```

**问题2：端口被占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3000
# 停止占用进程或修改端口配置
```

**问题3：API密钥无效**
- 检查API密钥是否正确
- 确认API密钥有足够额度
- 重启服务：`docker-compose restart`

**问题4：内存不足**
```bash
# 清理Docker资源
docker system prune -a
# 或只部署单个应用
```

### 获取帮助
- 📋 查看详细日志：`docker-compose logs -f [服务名]`
- 🔍 检查服务状态：`docker-compose ps`
- 📖 参考完整文档：[部署操作文档.md](./部署操作文档.md)

---

## 📞 技术支持

如果您在部署过程中遇到问题：

1. **查看日志**：大多数问题可以通过日志找到原因
2. **检查环境**：运行环境检查脚本确认系统状态
3. **参考文档**：查看详细的部署操作文档
4. **社区支持**：在项目Issues中寻求帮助

---

## 🎉 部署成功！

恭喜您成功部署AI智能应用平台！现在您可以：

1. **探索功能**：访问应用界面，体验AI功能
2. **上传数据**：添加您的数据库连接或文档
3. **开始使用**：尝试自然语言查询或问答
4. **定制配置**：根据需要调整模型和参数

享受AI带来的便利吧！🚀

---

**📝 注意事项**：
- 首次启动可能需要下载Docker镜像，请耐心等待
- 确保API密钥有足够的使用额度
- 定期备份重要数据
- 关注项目更新获取新功能
