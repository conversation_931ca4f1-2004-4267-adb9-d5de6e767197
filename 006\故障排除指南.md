# 🔧 故障排除指南

本指南帮助您解决部署过程中可能遇到的常见问题。

## 🚨 Docker权限问题

### 错误症状
```
PermissionError: [<PERSON>rrno 13] Permission denied
docker.errors.DockerException: Error while fetching server API version
```

### 解决方案

#### 方法1：使用修复脚本（推荐）
```bash
# 运行Docker权限修复脚本
chmod +x fix-docker-permissions.sh
./fix-docker-permissions.sh
```

#### 方法2：手动修复
```bash
# 1. 将用户添加到docker组
sudo usermod -aG docker $USER

# 2. 重新加载组权限（选择其中一种）
newgrp docker              # 方法A：在当前会话中生效
# 或者
logout && login            # 方法B：重新登录
# 或者
sudo reboot               # 方法C：重启系统

# 3. 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 4. 测试Docker权限
docker ps
```

#### 方法3：临时解决方案
```bash
# 仅用于紧急情况，不推荐长期使用
sudo chmod 666 /var/run/docker.sock
```

---

## 🔌 端口占用问题

### 错误症状
```
Error starting userland proxy: listen tcp 0.0.0.0:3000: bind: address already in use
port is already allocated
```

### 解决方案

#### 查找占用端口的进程
```bash
# 查看端口占用情况
sudo netstat -tlnp | grep :3000
# 或者
sudo lsof -i :3000
```

#### 停止占用进程
```bash
# 根据PID停止进程
sudo kill -9 <PID>

# 或者停止特定服务
sudo systemctl stop apache2    # 如果是Apache占用80端口
sudo systemctl stop nginx      # 如果是Nginx占用80端口
```

#### 修改端口配置
```bash
# 编辑docker-compose.yml文件
nano docker-compose.yml

# 修改端口映射，例如：
# 将 "3000:3000" 改为 "3001:3000"
```

---

## 🔑 API密钥问题

### 错误症状
```
Invalid API key
Authentication failed
Unauthorized
```

### 解决方案

#### 检查API密钥格式
```bash
# OpenAI API密钥格式
sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# DeepSeek API密钥格式
sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

#### 验证API密钥
```bash
# 测试OpenAI API密钥
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "Hello"}], "max_tokens": 5}' \
     https://api.openai.com/v1/chat/completions
```

#### 更新环境变量
```bash
# 编辑.env文件
nano .env

# 确保没有多余的空格或引号
OPENAI_API_KEY=sk-your-actual-key-here

# 重启服务
docker-compose restart
```

---

## 💾 内存不足问题

### 错误症状
```
Cannot allocate memory
Killed
Container keeps restarting
```

### 解决方案

#### 检查系统资源
```bash
# 查看内存使用情况
free -h

# 查看Docker容器资源使用
docker stats

# 查看磁盘空间
df -h
```

#### 释放内存
```bash
# 清理系统缓存
sudo sync && sudo sysctl vm.drop_caches=3

# 清理Docker资源
docker system prune -a
docker volume prune

# 停止不必要的服务
sudo systemctl stop snapd
sudo systemctl stop unattended-upgrades
```

#### 配置交换空间
```bash
# 创建2GB交换文件
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 永久启用
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

---

## 🌐 网络连接问题

### 错误症状
```
Network Error
Connection refused
Timeout
```

### 解决方案

#### 检查网络连接
```bash
# 测试互联网连接
ping -c 4 8.8.8.8

# 测试DNS解析
nslookup google.com

# 测试Docker Hub连接
curl -I https://hub.docker.com
```

#### 配置Docker镜像源（中国用户）
```bash
# 创建Docker配置目录
sudo mkdir -p /etc/docker

# 配置镜像源
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ]
}
EOF

# 重启Docker服务
sudo systemctl daemon-reload
sudo systemctl restart docker
```

---

## 🗄️ 数据库连接问题

### 错误症状
```
Connection refused
Database connection failed
Can't connect to MySQL server
```

### 解决方案

#### 检查数据库容器状态
```bash
# 查看所有容器状态
docker-compose ps

# 查看数据库日志
docker-compose logs mysql
docker-compose logs neo4j
docker-compose logs postgres
```

#### 重启数据库服务
```bash
# 重启特定服务
docker-compose restart mysql
docker-compose restart neo4j

# 或重启所有服务
docker-compose restart
```

#### 等待数据库完全启动
```bash
# 数据库需要时间初始化，等待30-60秒
sleep 60

# 检查数据库是否就绪
docker-compose exec mysql mysqladmin ping -h localhost
```

---

## 🔧 服务启动失败

### 错误症状
```
Service failed to start
Container exited with code 1
Build failed
```

### 解决方案

#### 查看详细日志
```bash
# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 查看构建日志
docker-compose build --no-cache backend
```

#### 清理并重新构建
```bash
# 停止所有服务
docker-compose down

# 清理Docker资源
docker system prune -a

# 重新构建并启动
docker-compose up -d --build
```

#### 检查配置文件
```bash
# 验证docker-compose.yml语法
docker-compose config

# 检查环境变量文件
cat .env
```

---

## 🚀 快速诊断命令

### 一键检查脚本
```bash
#!/bin/bash
echo "=== 系统诊断 ==="
echo "Docker版本: $(docker --version)"
echo "Docker Compose版本: $(docker-compose --version)"
echo "Docker服务状态: $(systemctl is-active docker)"
echo "用户组: $(groups $USER)"
echo "内存使用: $(free -h | grep Mem)"
echo "磁盘使用: $(df -h / | tail -1)"
echo "容器状态:"
docker ps -a
echo "Docker日志:"
docker-compose logs --tail=10
```

### 常用修复命令
```bash
# 完全重置（谨慎使用）
docker-compose down -v
docker system prune -a
docker volume prune

# 重新部署
docker-compose up -d --build
```

---

## 📞 获取帮助

如果以上方法都无法解决问题：

1. **收集信息**：
   ```bash
   # 生成诊断报告
   docker-compose logs > debug.log
   docker ps -a >> debug.log
   free -h >> debug.log
   df -h >> debug.log
   ```

2. **查看官方文档**：
   - Docker官方文档
   - 各应用的README文件

3. **社区支持**：
   - GitHub Issues
   - Stack Overflow
   - Docker社区论坛

4. **重新开始**：
   - 如果问题复杂，考虑重新安装Docker
   - 或使用全新的系统环境

---

## ⚡ 预防措施

1. **定期维护**：
   ```bash
   # 每周清理一次
   docker system prune
   
   # 每月更新一次
   sudo apt update && sudo apt upgrade
   ```

2. **监控资源**：
   ```bash
   # 设置资源监控
   watch -n 5 'docker stats --no-stream'
   ```

3. **备份配置**：
   ```bash
   # 备份重要配置
   cp .env .env.backup
   cp docker-compose.yml docker-compose.yml.backup
   ```

记住：大多数问题都有解决方案，保持耐心！🚀
