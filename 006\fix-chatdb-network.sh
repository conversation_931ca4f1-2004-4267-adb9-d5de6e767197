#!/bin/bash

# ChatDB网络连接问题修复脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

echo "========================================"
echo "   ChatDB网络连接问题修复脚本"
echo "========================================"
echo

# 检查是否在正确的目录
if [[ ! -f "docker-compose.yml" ]]; then
    print_error "请在chatdb目录下运行此脚本"
    exit 1
fi

print_info "开始修复ChatDB网络连接问题..."

# 1. 检查当前服务状态
print_info "1. 检查当前服务状态..."
docker-compose ps

echo
print_info "2. 检查后端服务..."

# 检查后端容器是否运行
if ! docker-compose ps backend | grep -q "Up"; then
    print_warning "后端容器未运行，正在启动..."
    docker-compose up -d backend
    sleep 15
fi

# 检查后端日志中的错误
print_info "检查后端日志..."
docker-compose logs --tail=20 backend

# 测试后端API
print_info "测试后端API连接..."
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -s http://localhost:8000/api/ > /dev/null 2>&1; then
        print_success "后端API连接成功！"
        break
    else
        print_warning "尝试 $attempt/$max_attempts: 后端API未响应，等待5秒..."
        sleep 5
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    print_error "后端API连接失败，尝试重启后端服务..."
    
    # 重启后端服务
    docker-compose restart backend
    sleep 20
    
    # 再次测试
    if curl -s http://localhost:8000/api/ > /dev/null 2>&1; then
        print_success "重启后后端API连接成功！"
    else
        print_error "后端API仍然无法连接"
        print_info "查看详细的后端日志："
        docker-compose logs backend
        exit 1
    fi
fi

echo
print_info "3. 检查数据库连接..."

# 检查MySQL连接
if docker-compose exec -T mysql mysql -u root -ppassword -e "SELECT 1;" > /dev/null 2>&1; then
    print_success "MySQL连接正常"
else
    print_warning "MySQL连接有问题，尝试重启MySQL..."
    docker-compose restart mysql
    sleep 15
    
    if docker-compose exec -T mysql mysql -u root -ppassword -e "SELECT 1;" > /dev/null 2>&1; then
        print_success "MySQL重启后连接正常"
    else
        print_error "MySQL连接仍然有问题"
    fi
fi

# 检查数据库是否存在
print_info "检查chatdb数据库..."
if docker-compose exec -T mysql mysql -u root -ppassword -e "USE chatdb; SELECT 1;" > /dev/null 2>&1; then
    print_success "chatdb数据库存在"
else
    print_warning "chatdb数据库不存在，正在创建..."
    docker-compose exec -T mysql mysql -u root -ppassword -e "CREATE DATABASE IF NOT EXISTS chatdb;"
    print_success "chatdb数据库已创建"
fi

# 初始化数据库表
print_info "检查数据库表..."
if docker-compose exec -T mysql mysql -u root -ppassword chatdb -e "SHOW TABLES;" 2>/dev/null | grep -q "db_connections"; then
    print_success "数据库表已存在"
else
    print_warning "数据库表不存在，正在初始化..."
    docker-compose exec backend python init_db.py
    print_success "数据库初始化完成"
fi

echo
print_info "4. 检查前端配置..."

# 检查前端环境变量
print_info "检查前端API配置..."
frontend_api_url=$(docker-compose exec frontend printenv REACT_APP_API_URL 2>/dev/null || echo "未设置")
print_info "前端API URL: $frontend_api_url"

if [[ "$frontend_api_url" != "http://localhost:8000/api" ]]; then
    print_warning "前端API URL配置可能有问题"
    print_info "重新构建前端容器..."
    docker-compose build --no-cache frontend
    docker-compose up -d frontend
    sleep 15
fi

echo
print_info "5. 测试完整的API链路..."

# 测试数据库连接API
print_info "测试数据库连接API..."
if curl -s http://localhost:8000/api/connections/ > /dev/null 2>&1; then
    print_success "数据库连接API正常"
    
    # 显示API响应
    print_info "API响应内容:"
    curl -s http://localhost:8000/api/connections/ | head -c 200
    echo
else
    print_error "数据库连接API失败"
    print_info "尝试重启后端服务..."
    docker-compose restart backend
    sleep 20
fi

echo
print_info "6. 检查网络连通性..."

# 检查容器间网络
if docker-compose exec backend ping -c 1 mysql > /dev/null 2>&1; then
    print_success "后端可以连接到MySQL"
else
    print_error "后端无法连接到MySQL"
fi

# 检查端口是否被占用
print_info "检查端口占用情况..."
if netstat -tlnp 2>/dev/null | grep -q ":8000"; then
    print_success "端口8000正在监听"
else
    print_warning "端口8000未在监听"
fi

if netstat -tlnp 2>/dev/null | grep -q ":3000"; then
    print_success "端口3000正在监听"
else
    print_warning "端口3000未在监听"
fi

echo
print_info "7. 最终验证..."

# 等待服务完全启动
print_info "等待所有服务完全启动..."
sleep 10

# 最终测试
if curl -s http://localhost:8000/api/ > /dev/null 2>&1; then
    print_success "后端API最终测试通过"
    
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_success "前端访问最终测试通过"
        
        echo
        print_success "========== 修复完成 =========="
        print_info "请刷新浏览器页面: http://localhost:3000"
        print_info "如果问题仍然存在，请检查浏览器控制台的网络请求"
        
    else
        print_warning "前端访问仍有问题"
    fi
else
    print_error "后端API仍有问题"
    print_info "请查看详细日志: docker-compose logs backend"
fi

echo
print_info "服务状态总览:"
docker-compose ps
