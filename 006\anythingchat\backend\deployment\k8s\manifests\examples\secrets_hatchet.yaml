---
apiVersion: v1
data:
  ADMIN_EMAIL: ++++++++
  ADMIN_PASSWORD: ++++++++
  DATABASE_POSTGRES_DB_NAME: ++++++++
  DATABASE_POSTGRES_HOST: ++++++++
  DATABASE_POSTGRES_PASSWORD: ++++++++
  DATABASE_POSTGRES_PORT: ++++++++
  DATABASE_POSTGRES_SSL_MODE: ++++++++
  DATABASE_POSTGRES_USERNAME: ++++++++
  DATABASE_URL: ++++++++
  SERVER_AUTH_BASIC_AUTH_ENABLED: ++++++++
  SERVER_AUTH_COOKIE_DOMAIN: ++++++++
  SERVER_AUTH_COOKIE_INSECURE: ++++++++
  SERVER_AUTH_SET_EMAIL_VERIFIED: ++++++++
  SERVER_GRPC_BIND_ADDRESS: ++++++++
  SERVER_GRPC_BROADCAST_ADDRESS: ++++++++
  SERVER_GRPC_INSECURE: ++++++++
  SERVER_TASKQUEUE_RABBITMQ_URL: ++++++++
  SERVER_URL: ++++++++
kind: Secret
metadata:
  name: hatchet-shared-config
  namespace: ai-system
type: Opaque

---
apiVersion: v1
data:
  rabbitmq.conf: ++++++++
kind: Secret
metadata:
  name: hatchet-rabbitmq-config
  namespace: ai-system
type: Opaque
---
apiVersion: v1
data:
  rabbitmq-erlang-cookie: ++++++++
  rabbitmq-password: ++++++++
  rabbitmq-user: ++++++++
kind: Secret
metadata:
  name: hatchet-rabbitmq
  namespace: ai-system
type: Opaque
