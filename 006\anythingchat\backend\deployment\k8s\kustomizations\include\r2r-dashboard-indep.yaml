---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: r2r-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: r2r-dashboard
  template:
    metadata:
      labels:
        app: r2r-dashboard
    spec:
      containers:
      - name: r2r-dashboard
        image: emrgntcmplxty/r2r-dashboard:1.0.1
        ports:
        - containerPort: 3000
        env:
          - name: NEXT_PUBLIC_R2R_DEPLOYMENT_URL
            valueFrom:
              configMapKeyRef:
                name: r2r-configmap
                key: NEXT_PUBLIC_R2R_DEPLOYMENT_URL
          - name: NEXT_PUBLIC_HATCHET_DASHBOARD_URL
            valueFrom:
              configMapKeyRef:
                name: r2r-configmap
                key: NEXT_PUBLIC_HATCHET_DASHBOARD_URL
        # Optionally add a liveness/readiness probe as needed.
        # For example:
        # livenessProbe:
        #   httpGet:
        #     path: /live
        #     port: 3000
        #   initialDelaySeconds: 10
        #   periodSeconds: 10
        # readinessProbe:
        #   httpGet:
        #     path: /ready
        #     port: 3000
        #   initialDelaySeconds: 5
        #   periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: r2r-dashboard
spec:
  selector:
    app: r2r-dashboard
  ports:
  - port: 3000           # External port from docker-compose ${R2R_DASHBOARD_PORT:-7273}
    targetPort: 3000     # Container port as set in docker-compose
  type: ClusterIP
