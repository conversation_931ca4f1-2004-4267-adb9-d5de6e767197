# 📁 项目结构说明

本文档详细说明了AI智能应用平台的项目结构和各个文件的作用。

## 🗂️ 根目录结构

```
006/
├── 📖 README-部署指南.md          # 快速部署指南（推荐先看这个）
├── 📖 部署操作文档.md             # 详细部署文档
├── 📖 项目结构说明.md             # 本文件，项目结构说明
├── 🔧 check-environment.sh        # Linux环境检查脚本
├── 🔧 check-environment.bat       # Windows环境检查脚本
├── ⚡ quick-deploy.sh             # Linux一键部署脚本
├── ⚡ quick-deploy.bat            # Windows一键部署脚本
├── 🤖 chatdb/                     # ChatDB应用目录
├── 🧠 anythingchat/               # AnythingChat应用目录
├── 🧠 super-memory/               # Super Memory应用目录
├── 🎯 admin/                      # 管理后台应用
├── 📄 ai-document/                # AI文档处理应用
├── 👤 client/                     # 客户端应用
├── 📚 examples/                   # 示例和测试文件
├── 🔤 qwen3-embedding/            # Qwen3嵌入模型相关
├── 📊 vanna_source/               # Vanna原始版本
└── 📊 vanna_update/               # Vanna更新版本
```

---

## 🤖 ChatDB - Text2SQL智能问答系统

**推荐指数**: ⭐⭐⭐⭐⭐ (新手首选)

```
chatdb/
├── 📖 README.md                   # ChatDB使用说明
├── 🐳 docker-compose.yml          # Docker编排文件
├── 🔧 .env                        # 环境变量配置文件（需要创建）
├── 🖥️ backend/                    # 后端服务
│   ├── 🐳 Dockerfile              # 后端Docker镜像构建文件
│   ├── 📋 requirements.txt        # Python依赖包列表
│   ├── 🚀 main.py                 # FastAPI应用入口
│   ├── 🔧 init_db.py              # 数据库初始化脚本
│   ├── 🔧 init_hybrid_system.py   # 混合检索系统初始化
│   ├── 📁 app/                    # 应用核心代码
│   │   ├── 🌐 api/                # API路由
│   │   ├── ⚙️ core/               # 核心配置
│   │   ├── 🗄️ db/                 # 数据库相关
│   │   ├── 📊 models/             # 数据模型
│   │   ├── 📋 schemas/            # 数据验证模式
│   │   └── 🔧 services/           # 业务逻辑服务
│   └── 🧪 tests/                  # 测试文件
└── 🌐 frontend/                   # 前端服务
    ├── 🐳 Dockerfile              # 前端Docker镜像构建文件
    ├── 📦 package.json            # Node.js依赖配置
    ├── 📁 src/                    # React源代码
    │   ├── 🎨 components/         # React组件
    │   ├── 📄 pages/              # 页面组件
    │   ├── 🔧 services/           # API服务
    │   └── 🎨 styles/             # 样式文件
    └── 📁 public/                 # 静态资源
```

**主要功能**:
- 🗣️ 自然语言转SQL查询
- 📊 数据库schema可视化
- 🔗 多数据库连接管理
- 📈 查询结果展示

**访问地址**:
- 前端: http://localhost:3000
- 后端API: http://localhost:8000/docs
- Neo4j管理: http://localhost:7474

---

## 🧠 AnythingChat - 高级RAG和多智能体系统

**推荐指数**: ⭐⭐⭐⭐ (高级用户)

```
anythingchat/
├── 🖥️ backend/                    # 后端服务
│   ├── 🐳 docker/                 # Docker配置目录
│   │   ├── 🐳 docker-compose.yml  # Docker编排文件
│   │   ├── 🐳 compose.yaml        # 简化版编排文件
│   │   ├── 🔧 env/                # 环境变量配置目录
│   │   │   ├── 🔑 r2r.env         # R2R服务环境变量
│   │   │   ├── 🗄️ postgres.env    # PostgreSQL配置
│   │   │   └── 📦 minio.env       # MinIO对象存储配置
│   │   └── 🔧 user_configs/       # 用户配置目录
│   └── 🐍 py/                     # Python核心代码
│       ├── 🐳 Dockerfile          # Python服务Docker文件
│       └── ⚙️ core/               # 核心功能模块
└── 🌐 frontend/                   # 前端服务（Next.js）
    ├── 🐳 Dockerfile              # 前端Docker文件
    └── 📦 package.json            # 前端依赖配置
```

**主要功能**:
- 📚 文档智能问答（RAG）
- 🤖 多智能体协作
- 🔍 混合检索系统
- 🌐 多模型支持

**访问地址**:
- R2R Dashboard: http://localhost:7273
- R2R API: http://localhost:7272
- MinIO管理: http://localhost:9001

---

## 🧠 Super Memory - 智能记忆管理系统

**推荐指数**: ⭐⭐⭐ (特定场景)

```
super-memory/
├── 📋 requirements.txt            # Python依赖
├── 🖥️ server/                     # 服务端
│   ├── 🐳 docker-compose.yaml     # Docker编排文件
│   ├── 🐳 dev.Dockerfile          # 开发环境Docker文件
│   └── 🔧 .env                    # 环境变量（需要创建）
├── 📁 openmemory/                 # OpenMemory模块
│   └── 🐳 docker-compose.yml      # OpenMemory Docker配置
└── 📚 examples/                   # 使用示例
```

**主要功能**:
- 🧠 智能记忆存储
- 🔗 知识图谱构建
- 📝 上下文感知
- 🔄 记忆演化

**访问地址**:
- Memory API: http://localhost:8888
- Neo4j管理: http://localhost:8474

---

## 🎯 其他应用模块

### 🎯 Admin - 管理后台
```
admin/
├── 🖥️ backend/                    # Python FastAPI后端
│   ├── 📋 requirements.txt        # Python依赖
│   └── 🔧 a_app/                  # 应用核心代码
└── 🌐 frontend/                   # Vue.js前端
    ├── 📦 package.json            # 前端依赖
    └── 🔧 src/                    # Vue源代码
```

### 📄 AI-Document - AI文档处理
```
ai-document/
├── 🖥️ backend/                    # 文档处理后端
│   ├── 📋 requirements.txt        # Python依赖
│   └── 🔧 app/                    # 应用代码
└── 🌐 frontend/                   # 文档管理前端
    └── 📦 package.json            # 前端依赖
```

### 👤 Client - 客户端应用
```
client/
├── 🖥️ backend/                    # 客户端后端服务
└── 🌐 frontend/                   # 客户端前端界面
```

---

## 📚 支持文件和示例

### 📚 Examples - 示例文件
```
examples/
├── 🐍 autogen_*.py                # AutoGen多智能体示例
├── 🔍 chroma_*.py                 # ChromaDB向量数据库示例
├── 📄 marker_example.py           # 文档标记示例
├── 📊 *.pdf                       # 测试PDF文件
├── 🖼️ *.png                       # 测试图片文件
└── 📝 *.md                        # 测试Markdown文件
```

### 🔤 Qwen3-Embedding - 嵌入模型
```
qwen3-embedding/
├── 📖 *.pdf                       # Qwen3模型文档
├── 🤖 Qwen3-Embedding/            # 嵌入模型代码
├── 🔧 Qwen3-Embedding-SFT/        # 微调代码
└── 🔧 Qwen3-Reranker-SFT/         # 重排序模型
```

### 📊 Vanna - SQL生成工具
```
vanna_source/                      # 原始Vanna版本
├── 🚀 app.py                      # Flask应用
├── 🗄️ database.py                 # 数据库操作
└── 📋 requirements.txt            # 依赖包

vanna_update/                      # 更新Vanna版本
├── 🚀 app.py                      # 更新的Flask应用
├── 🌐 server.js                   # Node.js服务器
└── 📦 package.json                # Node.js依赖
```

---

## 🔧 配置文件说明

### 环境变量文件 (.env)
每个应用都需要相应的环境变量配置：

**ChatDB (.env)**:
```env
OPENAI_API_KEY=your_api_key_here
MYSQL_ROOT_PASSWORD=password
MYSQL_DATABASE=chatdb
NEO4J_AUTH=neo4j/password
REACT_APP_API_URL=http://localhost:8000/api
```

**AnythingChat (env/r2r.env)**:
```env
OPENAI_API_KEY=your_api_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
R2R_PORT=7272
R2R_HOST=0.0.0.0
# ... 更多配置
```

**Super Memory (.env)**:
```env
OPENAI_API_KEY=your_api_key_here
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
NEO4J_AUTH=neo4j/mem0graph
```

### Docker配置文件
- `docker-compose.yml`: 定义服务、网络、存储卷
- `Dockerfile`: 定义镜像构建过程
- `requirements.txt`: Python依赖包列表
- `package.json`: Node.js依赖包配置

---

## 🚀 部署建议

### 新手用户推荐顺序:
1. **ChatDB** - 功能完整，易于理解
2. **Super Memory** - 功能专一，资源占用少
3. **AnythingChat** - 功能强大，但配置复杂

### 资源分配建议:
- **单应用部署**: 4GB内存，20GB磁盘
- **双应用部署**: 8GB内存，40GB磁盘
- **全部部署**: 16GB内存，80GB磁盘

### 端口分配:
确保以下端口未被占用：
- 3000-3001: 前端服务
- 8000-8001: 后端API
- 3306: MySQL
- 5432: PostgreSQL
- 7272-7274: R2R服务
- 7474, 7687: Neo4j
- 9000-9001: MinIO

---

## 📞 获取帮助

如果您对项目结构有疑问：
1. 查看各应用目录下的README.md文件
2. 参考[部署操作文档.md](./部署操作文档.md)
3. 运行环境检查脚本确认系统状态
4. 在项目Issues中提问

**记住**: 从ChatDB开始是最好的选择！🚀
